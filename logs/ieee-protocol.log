2025-09-24T10:14:52.804+08:00  INFO 17628 --- [ieee-schedule-2] c.w.i.s.s.RedisTaskSchedulerService      : 扫描Redis任务,找到 1 个任务需要处理
2025-09-24T10:14:52.813+08:00  INFO 17628 --- [ieee-schedule-2] c.w.i.s.d.impl.EndDeviceServiceImpl      : THA133000222355020的setControl任务开始----------------
2025-09-24T10:14:52.818+08:00  INFO 17628 --- [ieee-schedule-2] c.w.i.s.p.i.SpecificServTuyaServiceImpl  : tuya write param is: {"deviceId":"","slaveId":1,"startAddress":61001,"len":6,"values":[1,1,0,0,100,35]}
2025-09-24T10:14:52.838+08:00  INFO 17628 --- [SpringApplicationShutdownHook] n.d.b.g.s.s.GrpcServerLifecycle          : Completed gRPC server shutdown
2025-09-24T10:14:52.845+08:00 ERROR 17628 --- [ieee-schedule-2] c.w.i.s.d.impl.EndDeviceServiceImpl      : THA133000222355020 send write command error: 

java.lang.RuntimeException: call ecos iot error
	at com.weihengtech.ieee.service.passthrough.impl.CacheServiceImpl.getIotCloudRes(CacheServiceImpl.java:58) ~[classes/:na]
	at com.weihengtech.ieee.service.passthrough.impl.CacheServiceImpl.getDeviceBasicInfo(CacheServiceImpl.java:33) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.cache.interceptor.CacheAspectSupport.invokeOperation(CacheAspectSupport.java:366) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:420) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:345) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-6.0.13.jar:6.0.13]
	at com.weihengtech.ieee.service.passthrough.impl.CacheServiceImpl$$SpringCGLIB$$0.getDeviceBasicInfo(<generated>) ~[classes/:na]
	at com.weihengtech.ieee.service.passthrough.impl.IotClientServiceImpl.writeDeviceAsync(IotClientServiceImpl.java:93) ~[classes/:na]
	at com.weihengtech.ieee.service.passthrough.impl.SpecificServTuyaServiceImpl.sendWriteCommandAsync(SpecificServTuyaServiceImpl.java:48) ~[classes/:na]
	at com.weihengtech.ieee.service.device.impl.EndDeviceServiceImpl.doAction(EndDeviceServiceImpl.java:169) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.retry.annotation.AnnotationAwareRetryOperationsInterceptor.invoke(AnnotationAwareRetryOperationsInterceptor.java:163) ~[spring-retry-2.0.4.jar:na]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-6.0.13.jar:6.0.13]
	at com.weihengtech.ieee.service.device.impl.EndDeviceServiceImpl$$SpringCGLIB$$0.doAction(<generated>) ~[classes/:na]
	at com.weihengtech.ieee.service.scheduler.RedisTaskSchedulerService.processTask(RedisTaskSchedulerService.java:72) ~[classes/:na]
	at com.weihengtech.ieee.service.scheduler.RedisTaskSchedulerService.scanAndExecuteRedisTasks(RedisTaskSchedulerService.java:55) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-6.0.13.jar:6.0.13]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-09-24T10:14:52.845+08:00 ERROR 17628 --- [ieee-schedule-2] c.w.i.c.ThreadPoolTaskExecutorConfig     : Task error

org.springframework.retry.RetryException: THA133000222355020 send write command error: 
	at com.weihengtech.ieee.service.device.impl.EndDeviceServiceImpl.doAction(EndDeviceServiceImpl.java:173) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.retry.annotation.AnnotationAwareRetryOperationsInterceptor.invoke(AnnotationAwareRetryOperationsInterceptor.java:163) ~[spring-retry-2.0.4.jar:na]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751) ~[spring-aop-6.0.13.jar:6.0.13]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-6.0.13.jar:6.0.13]
	at com.weihengtech.ieee.service.device.impl.EndDeviceServiceImpl$$SpringCGLIB$$0.doAction(<generated>) ~[classes/:na]
	at com.weihengtech.ieee.service.scheduler.RedisTaskSchedulerService.processTask(RedisTaskSchedulerService.java:72) ~[classes/:na]
	at com.weihengtech.ieee.service.scheduler.RedisTaskSchedulerService.scanAndExecuteRedisTasks(RedisTaskSchedulerService.java:55) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-6.0.13.jar:6.0.13]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-09-24T10:17:19.438+08:00  INFO 28308 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 28308 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol)
2025-09-24T10:17:19.439+08:00  INFO 28308 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-24T10:17:20.313+08:00  INFO 28308 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-24T10:17:20.315+08:00  INFO 28308 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-24T10:17:20.353+08:00  INFO 28308 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2025-09-24T10:17:21.057+08:00  INFO 28308 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-24T10:17:21.065+08:00  INFO 28308 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-24T10:17:21.065+08:00  INFO 28308 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-24T10:17:21.139+08:00  INFO 28308 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-24T10:17:21.139+08:00  INFO 28308 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1659 ms
2025-09-24T10:17:22.302+08:00  INFO 28308 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-24T10:17:22.593+08:00  INFO 28308 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 13 endpoint(s) beneath base path '/actuator'
2025-09-24T10:17:22.637+08:00  INFO 28308 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-09-24T10:17:22.710+08:00  INFO 28308 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: rpc.EndDeviceService, bean: endDeviceServer, class: com.weihengtech.ieee.server.EndDeviceServer
2025-09-24T10:17:22.711+08:00  INFO 28308 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-09-24T10:17:22.711+08:00  INFO 28308 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-09-24T10:17:22.712+08:00  INFO 28308 --- [main] c.w.ieee.config.GrpcServerConfiguration  : gRPC server configured to use virtual threads executor
2025-09-24T10:17:22.828+08:00  INFO 28308 --- [main] n.d.b.g.s.s.GrpcServerLifecycle          : gRPC Server started, listening on address: *, port: 9090
2025-09-24T10:17:29.095+08:00  INFO 28308 --- [main] c.w.ieee.IeeeServerApplication           : Started IeeeServerApplication in 9.997 seconds (process running for 10.632)
2025-09-24T10:17:29.823+08:00  INFO 28308 --- [RMI TCP Connection(1)-***********] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-24T10:17:29.824+08:00  INFO 28308 --- [RMI TCP Connection(1)-***********] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-24T10:17:29.825+08:00  INFO 28308 --- [RMI TCP Connection(1)-***********] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-24T10:17:29.841+08:00  INFO 28308 --- [RMI TCP Connection(3)-***********] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-09-24T10:17:36.622+08:00  INFO 28308 --- [] c.w.i.i.CustomServerCallListener         : rpc request param is: lfdi: "65d3c0e53ae8413aaadd0f610f8eabd300062011"
id: 1764612620318543872
mrid: "demo123124"
control {
  opModExpLimW: 1000
}
type: "start"
start_time: 1758680256
duration: 3000
, thread:  (virtual: true)
2025-09-24T10:17:36.774+08:00 ERROR 28308 --- [] c.w.i.s.d.impl.EndDeviceServiceImpl      : device: 1764612620318543872 is offline
2025-09-24T10:18:10.555+08:00 ERROR 28308 --- [ieee-schedule-3] c.w.i.c.ThreadPoolTaskExecutorConfig     : Task error

org.springframework.dao.QueryTimeoutException: Redis command timed out
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:68) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:38) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:256) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:969) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:826) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:673) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$DefaultManyInvocationSpec.toSet(LettuceInvoker.java:639) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$ManyInvocationSpec.toSet(LettuceInvoker.java:434) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:124) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$10(RedisTemplate.java:638) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.core.RedisTemplate.lambda$doWithKeys$22(RedisTemplate.java:785) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:406) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:373) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.core.RedisTemplate.doWithKeys(RedisTemplate.java:785) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:638) ~[spring-data-redis-3.1.5.jar:3.1.5]
	at com.weihengtech.ieee.service.scheduler.RedisTaskSchedulerService.scanAndExecuteRedisTasks(RedisTaskSchedulerService.java:47) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-6.0.13.jar:6.0.13]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 2 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59) ~[lettuce-core-6.2.6.RELEASE.jar:6.2.6.RELEASE]
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246) ~[lettuce-core-6.2.6.RELEASE.jar:6.2.6.RELEASE]
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74) ~[lettuce-core-6.2.6.RELEASE.jar:6.2.6.RELEASE]
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:967) ~[spring-data-redis-3.1.5.jar:3.1.5]
	... 23 common frames omitted

2025-09-24T10:18:10.690+08:00  INFO 28308 --- [ieee-schedule-3] c.w.i.s.s.RedisTaskSchedulerService      : 扫描Redis任务,找到 1 个任务需要处理
2025-09-24T10:18:10.693+08:00  INFO 28308 --- [] c.w.ieee.interceptor.CustomServerCall    : elapsed : 34139ms, rpc response is: , thread:  (virtual: true)
2025-09-24T10:18:10.702+08:00  INFO 28308 --- [SpringApplicationShutdownHook] n.d.b.g.s.s.GrpcServerLifecycle          : Completed gRPC server shutdown
2025-09-24T10:32:17.586+08:00  INFO 17228 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 17228 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol)
2025-09-24T10:32:17.588+08:00  INFO 17228 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-24T10:32:18.447+08:00  INFO 17228 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-24T10:32:18.450+08:00  INFO 17228 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-24T10:32:18.486+08:00  INFO 17228 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-09-24T10:32:19.179+08:00  INFO 17228 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-24T10:32:19.186+08:00  INFO 17228 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-24T10:32:19.186+08:00  INFO 17228 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-24T10:32:19.255+08:00  INFO 17228 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-24T10:32:19.255+08:00  INFO 17228 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1622 ms
2025-09-24T10:32:19.961+08:00  WARN 17228 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'endDeviceServer': Injection of resource dependencies failed
2025-09-24T10:32:19.971+08:00  INFO 17228 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-0} closing ...
2025-09-24T10:32:19.973+08:00  INFO 17228 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-09-24T10:32:19.995+08:00  INFO 17228 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-24T10:32:20.010+08:00 ERROR 17228 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'endDeviceServer': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:323) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1416) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:950) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-3.1.5.jar:3.1.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295) ~[spring-boot-3.1.5.jar:3.1.5]
	at com.weihengtech.ieee.IeeeServerApplication.main(IeeeServerApplication.java:28) ~[classes/:na]
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'endDeviceServiceImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:323) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1416) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1417) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1337) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:531) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:508) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:659) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:320) ~[spring-context-6.0.13.jar:6.0.13]
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.BeanNotOfRequiredTypeException: Bean named 'redisTemplate' is expected to be of type 'org.springframework.data.redis.core.StringRedisTemplate' but was actually of type 'org.springframework.data.redis.core.RedisTemplate'
	at org.springframework.beans.factory.support.AbstractBeanFactory.adaptBeanInstance(AbstractBeanFactory.java:407) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:388) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:457) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:537) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:508) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:659) ~[spring-context-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.0.13.jar:6.0.13]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:320) ~[spring-context-6.0.13.jar:6.0.13]
	... 33 common frames omitted

2025-09-24T10:33:09.386+08:00  INFO 19368 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 19368 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol)
2025-09-24T10:33:09.387+08:00  INFO 19368 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-24T10:33:10.219+08:00  INFO 19368 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-24T10:33:10.221+08:00  INFO 19368 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-24T10:33:10.258+08:00  INFO 19368 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-09-24T10:33:10.946+08:00  INFO 19368 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-24T10:33:10.953+08:00  INFO 19368 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-24T10:33:10.953+08:00  INFO 19368 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-24T10:33:11.017+08:00  INFO 19368 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-24T10:33:11.017+08:00  INFO 19368 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1586 ms
2025-09-24T10:33:12.141+08:00  INFO 19368 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-24T10:33:12.426+08:00  INFO 19368 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 13 endpoint(s) beneath base path '/actuator'
2025-09-24T10:33:12.475+08:00  INFO 19368 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-09-24T10:33:12.532+08:00  INFO 19368 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: rpc.EndDeviceService, bean: endDeviceServer, class: com.weihengtech.ieee.server.EndDeviceServer
2025-09-24T10:33:12.532+08:00  INFO 19368 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-09-24T10:33:12.532+08:00  INFO 19368 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-09-24T10:33:12.533+08:00  INFO 19368 --- [main] c.w.ieee.config.GrpcServerConfiguration  : gRPC server configured to use virtual threads executor
2025-09-24T10:33:12.642+08:00  INFO 19368 --- [main] n.d.b.g.s.s.GrpcServerLifecycle          : gRPC Server started, listening on address: *, port: 9090
2025-09-24T10:33:12.652+08:00  INFO 19368 --- [main] c.w.ieee.IeeeServerApplication           : Started IeeeServerApplication in 3.602 seconds (process running for 4.236)
2025-09-24T10:33:13.334+08:00  INFO 19368 --- [RMI TCP Connection(3)-***********] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-24T10:33:13.335+08:00  INFO 19368 --- [RMI TCP Connection(3)-***********] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-24T10:33:13.336+08:00  INFO 19368 --- [RMI TCP Connection(3)-***********] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-24T10:33:13.354+08:00  INFO 19368 --- [RMI TCP Connection(1)-***********] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-09-24T10:33:23.710+08:00  INFO 19368 --- [] c.w.i.i.CustomServerCallListener         : rpc request param is: lfdi: "65d3c0e53ae8413aaadd0f610f8eabd300062011"
id: 1764612620318543872
mrid: "demo123124"
control {
  opModExpLimW: 1000
}
type: "start"
start_time: 1758681203
duration: 3000
, thread:  (virtual: true)
