# Redis缓存Proto对象序列化问题解决方案

## 问题描述

在使用Redis缓存`CacheItemDTO`对象时，由于该对象包含了`EndDeviceProto.DERControlRequest`这个Protocol Buffer对象，导致Jackson序列化时出现循环引用错误：

```
SerializationException: Could not write JSON: Direct self-reference leading to cycle 
(through reference chain: CacheItemDTO["request"]->EndDeviceProto$DERControlRequest["unknownFields"]->UnknownFieldSet["defaultInstanceForType"])
```

## 根本原因

Protocol Buffer生成的Java类包含内部字段（如`unknownFields`、`defaultInstanceForType`等），这些字段在Jackson序列化时会产生循环引用，导致序列化失败。

## 解决方案

### 方案1：修改Redis配置（已实施）

修改`RedisConfig.java`，配置Jackson ObjectMapper忽略循环引用：

```java
// 配置Jackson处理循环引用和Proto对象序列化问题
om.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
om.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
```

### 方案2：添加Jackson注解（已实施）

在`CacheItemDTO`类上添加`@JsonIgnoreProperties(ignoreUnknown = true)`注解，忽略未知属性。

### 方案3：自定义Proto序列化器（可选）

创建了`ProtoSerializationUtil`工具类，提供专门的Proto对象序列化器，可在需要时使用。

## 修改的文件

1. `ieee-core/src/main/java/com/weihengtech/ieee/config/RedisConfig.java`
   - 添加了Jackson配置来处理循环引用问题

2. `ieee-core/src/main/java/com/weihengtech/ieee/pojo/dtos/CacheItemDTO.java`
   - 添加了`@JsonIgnoreProperties(ignoreUnknown = true)`注解

3. `ieee-core/src/main/java/com/weihengtech/ieee/utils/ProtoSerializationUtil.java`（新增）
   - 提供了专门的Proto对象序列化工具类

4. `ieee-core/src/test/java/com/weihengtech/ieee/config/RedisSerializationTest.java`（新增）
   - 添加了Redis序列化测试用例

## 验证方法

运行测试用例验证修复效果：

```bash
mvn test -Dtest=RedisSerializationTest
```

## 优势

1. **不改变业务逻辑**：完全保持原有代码逻辑不变
2. **向后兼容**：不影响现有功能
3. **性能友好**：配置级别的修改，不增加运行时开销
4. **可扩展**：为将来可能的其他Proto对象序列化需求提供了基础

## 注意事项

1. 修改后的配置会影响所有通过该RedisTemplate序列化的对象
2. 如果需要更精细的控制，可以考虑为特定的对象类型创建专门的序列化器
3. 建议在生产环境部署前进行充分测试

## 测试建议

1. 运行单元测试确保序列化/反序列化正常工作
2. 在测试环境验证完整的业务流程
3. 监控Redis中缓存对象的大小和性能表现
