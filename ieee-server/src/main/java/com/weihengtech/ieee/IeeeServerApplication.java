package com.weihengtech.ieee;

import com.weihengtech.ieee.delay.InitUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2024/7/24 16:24
 * @version 1.0
 */
@SpringBootApplication(
    scanBasePackages = {"com.weihengtech.ieee"}
        ,
    exclude = {
        org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration.class
    }
)
@EnableRetry
@EnableCaching
@EnableScheduling
public class IeeeServerApplication {

    public static void main(String[] args) {
        InitUtil.init(SpringApplication.run(IeeeServerApplication.class, args));
    }
}