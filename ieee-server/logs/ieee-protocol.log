2025-09-24T09:55:29.778+08:00  INFO 34708 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 34708 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server)
2025-09-24T09:55:29.780+08:00  INFO 34708 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-24T09:55:30.434+08:00  INFO 34708 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-24T09:55:30.436+08:00  INFO 34708 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-24T09:55:30.465+08:00  INFO 34708 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-09-24T09:55:31.047+08:00  INFO 34708 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-24T09:55:31.055+08:00  INFO 34708 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-24T09:55:31.055+08:00  INFO 34708 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-24T09:55:31.121+08:00  INFO 34708 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-24T09:55:31.122+08:00  INFO 34708 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1305 ms
2025-09-24T09:55:32.049+08:00  INFO 34708 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-24T09:55:32.288+08:00  INFO 34708 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 13 endpoint(s) beneath base path '/actuator'
2025-09-24T09:55:32.317+08:00  WARN 34708 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-09-24T09:55:32.325+08:00  INFO 34708 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-0} closing ...
2025-09-24T09:55:32.326+08:00  INFO 34708 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-09-24T09:55:32.383+08:00  INFO 34708 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-24T09:55:32.393+08:00 ERROR 34708 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-09-24T09:56:16.022+08:00  INFO 4728 --- [main] c.w.ieee.IeeeServerApplication           : Starting IeeeServerApplication using Java 21.0.6 with PID 4728 (D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server\target\classes started by lujie.shen in D:\Program Files\JetBrains\IdeaProjects\ieee-protocol\ieee-server)
2025-09-24T09:56:16.024+08:00  INFO 4728 --- [main] c.w.ieee.IeeeServerApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-24T09:56:16.674+08:00  INFO 4728 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-24T09:56:16.676+08:00  INFO 4728 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-24T09:56:16.702+08:00  INFO 4728 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-09-24T09:56:17.286+08:00  INFO 4728 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-09-24T09:56:17.293+08:00  INFO 4728 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-24T09:56:17.293+08:00  INFO 4728 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-09-24T09:56:17.358+08:00  INFO 4728 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-24T09:56:17.358+08:00  INFO 4728 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1300 ms
2025-09-24T09:56:18.289+08:00  INFO 4728 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-09-24T09:56:18.536+08:00  INFO 4728 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 13 endpoint(s) beneath base path '/actuator'
2025-09-24T09:56:18.575+08:00  INFO 4728 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-09-24T09:56:18.616+08:00  INFO 4728 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: rpc.EndDeviceService, bean: endDeviceServer, class: com.weihengtech.ieee.server.EndDeviceServer
2025-09-24T09:56:18.616+08:00  INFO 4728 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-09-24T09:56:18.617+08:00  INFO 4728 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-09-24T09:56:18.618+08:00  INFO 4728 --- [main] c.w.ieee.config.GrpcServerConfiguration  : gRPC server configured to use virtual threads executor
2025-09-24T09:56:18.702+08:00  INFO 4728 --- [main] n.d.b.g.s.s.GrpcServerLifecycle          : gRPC Server started, listening on address: *, port: 9090
2025-09-24T09:56:18.710+08:00  INFO 4728 --- [main] c.w.ieee.IeeeServerApplication           : Started IeeeServerApplication in 2.961 seconds (process running for 3.201)
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#73]/runnable@ForkJoinPool-1-worker-7 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#104]/runnable@ForkJoinPool-1-worker-6 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#162]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.715+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#63]/runnable@ForkJoinPool-1-worker-3 start
2025-09-24T09:56:18.715+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#60]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.715+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#62]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.715+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#65]/runnable@ForkJoinPool-1-worker-5 start
2025-09-24T09:56:18.715+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#64]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#66]/runnable@ForkJoinPool-1-worker-5 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#71]/runnable@ForkJoinPool-1-worker-5 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#74]/runnable@ForkJoinPool-1-worker-5 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#72]/runnable@ForkJoinPool-1-worker-5 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#75]/runnable@ForkJoinPool-1-worker-5 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#76]/runnable@ForkJoinPool-1-worker-5 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#77]/runnable@ForkJoinPool-1-worker-5 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#69]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#78]/runnable@ForkJoinPool-1-worker-5 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#79]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#68]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#82]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#84]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#81]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#67]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#87]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#80]/runnable@ForkJoinPool-1-worker-5 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#70]/runnable@ForkJoinPool-1-worker-6 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#90]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#88]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#89]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#91]/runnable@ForkJoinPool-1-worker-5 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#93]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#92]/runnable@ForkJoinPool-1-worker-6 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#95]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#96]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#97]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#98]/runnable@ForkJoinPool-1-worker-6 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#101]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#100]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#103]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#105]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#102]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#106]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#107]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#109]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#110]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#111]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#112]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#113]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#108]/runnable@ForkJoinPool-1-worker-12 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#115]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#116]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#118]/runnable@ForkJoinPool-1-worker-12 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#119]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#121]/runnable@ForkJoinPool-1-worker-12 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#120]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#117]/runnable@ForkJoinPool-1-worker-9 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#123]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#122]/runnable@ForkJoinPool-1-worker-14 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#124]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#128]/runnable@ForkJoinPool-1-worker-7 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#125]/runnable@ForkJoinPool-1-worker-12 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#129]/runnable@ForkJoinPool-1-worker-9 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#133]/runnable@ForkJoinPool-1-worker-14 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#130]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#132]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#131]/runnable@ForkJoinPool-1-worker-7 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#136]/runnable@ForkJoinPool-1-worker-14 start
2025-09-24T09:56:18.716+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#114]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#138]/runnable@ForkJoinPool-1-worker-7 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#137]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#134]/runnable@ForkJoinPool-1-worker-12 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#135]/runnable@ForkJoinPool-1-worker-9 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#140]/runnable@ForkJoinPool-1-worker-14 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#141]/runnable@ForkJoinPool-1-worker-7 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#139]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#142]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#143]/runnable@ForkJoinPool-1-worker-12 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#147]/runnable@ForkJoinPool-1-worker-7 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#146]/runnable@ForkJoinPool-1-worker-14 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#144]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#150]/runnable@ForkJoinPool-1-worker-7 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#151]/runnable@ForkJoinPool-1-worker-14 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#148]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#149]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#152]/runnable@ForkJoinPool-1-worker-12 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#153]/runnable@ForkJoinPool-1-worker-7 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#154]/runnable@ForkJoinPool-1-worker-14 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#145]/runnable@ForkJoinPool-1-worker-9 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#156]/runnable@ForkJoinPool-1-worker-1 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#158]/runnable@ForkJoinPool-1-worker-7 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#160]/runnable@ForkJoinPool-1-worker-14 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#157]/runnable@ForkJoinPool-1-worker-2 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#159]/runnable@ForkJoinPool-1-worker-12 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#161]/runnable@ForkJoinPool-1-worker-9 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#155]/runnable@ForkJoinPool-1-worker-4 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#83]/runnable@ForkJoinPool-1-worker-8 start
2025-09-24T09:56:18.717+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#85]/runnable@ForkJoinPool-1-worker-13 start
2025-09-24T09:56:18.718+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#86]/runnable@ForkJoinPool-1-worker-11 start
2025-09-24T09:56:18.718+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#94]/runnable@ForkJoinPool-1-worker-10 start
2025-09-24T09:56:18.718+08:00  INFO 4728 --- [] com.weihengtech.ieee.delay.InitUtil      : DelayQueue Consumer Thread VirtualThread[#99]/runnable@ForkJoinPool-1-worker-5 start
2025-09-24T10:01:00.275+08:00  INFO 4728 --- [SpringApplicationShutdownHook] n.d.b.g.s.s.GrpcServerLifecycle          : Completed gRPC server shutdown
2025-09-24T10:01:00.335+08:00  INFO 4728 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-0} closing ...
