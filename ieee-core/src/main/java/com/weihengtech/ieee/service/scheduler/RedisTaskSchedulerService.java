package com.weihengtech.ieee.service.scheduler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.constants.Constants;
import com.weihengtech.ieee.pojo.dos.DeviceListDO;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import com.weihengtech.ieee.service.device.impl.EndDeviceServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * Redis任务定时调度服务
 * 定时扫描Redis中的任务，并执行相应的业务逻辑
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/23
 */
@Service
@Slf4j
public class RedisTaskSchedulerService {

    @Value("${current.grid.company:3}")
    private int currentGridCompany;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private EndDeviceServiceImpl endDeviceService;

    /**
     * 定时任务：扫描Redis中的任务并执行
     * 使用fixedDelayString从配置文件读取执行间隔，单位为毫秒
     */
    @Scheduled(fixedDelayString = "${redis.task.scan.interval:60000}")
    public void scanAndExecuteRedisTasks() {
        // 构建任务key的匹配模式
        String taskKeyPattern = String.format(Constants.TASK_KEY_PATTERN, currentGridCompany);
        // 获取所有匹配的keys
        Set<String> taskKeys = stringRedisTemplate.keys(taskKeyPattern);
        if (CollUtil.isEmpty(taskKeys)) {
            log.debug("未找到匹配的任务key");
            return;
        }
        log.info("扫描Redis任务,找到 {} 个任务需要处理", taskKeys.size());
        // 遍历每个key，获取对应的value并执行任务
        for (String taskKey : taskKeys) {
            processTask(taskKey);
        }
    }

    /**
     * 处理单个任务
     *
     * @param taskKey 任务key
     */
    private void processTask(String taskKey) {
        // 获取任务数据
        String cacheStr = stringRedisTemplate.opsForValue().get(taskKey);
        if (cacheStr == null) {
            log.warn("任务 {} 的值为空，跳过处理", taskKey);
            return;
        }
        DeviceListDO deviceInfo = JSONUtil.toBean(cacheStr.split("\\|")[1], DeviceListDO.class);
        StorageEnergyInfoDO storageInfo = JSONUtil.toBean(cacheStr.split("\\|")[2], StorageEnergyInfoDO.class);
        String requestStr = cacheStr.split("\\|")[3];
        JsonFormat.Parser parser = JsonFormat.parser().ignoringUnknownFields();
        EndDeviceProto.DERControlRequest.Builder builder = EndDeviceProto.DERControlRequest.newBuilder();
        EndDeviceProto.DERControlRequest request;
        try {
            parser.merge(requestStr, builder);
            request = builder.build();
        } catch (InvalidProtocolBufferException e) {
            throw new RuntimeException(e);
        }
        endDeviceService.doAction(request, storageInfo, deviceInfo);
    }
}
