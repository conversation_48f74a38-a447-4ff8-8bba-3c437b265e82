package com.weihengtech.ieee.delay;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.concurrent.DelayQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date 2025/6/25 18:56
 * @version 1.0
 */
@Slf4j
public class InitUtil {

	public static ConfigurableApplicationContext APPLICATION_CONTEXT;
	public static DelayQueue<DelayQueueMessage> DELAY_QUEUE = new DelayQueue<>();

	public static void init(ConfigurableApplicationContext applicationContext) {
		APPLICATION_CONTEXT = applicationContext;
//		initDelayQueueTaskThread();
	}

	public static void initDelayQueueTaskThread() {
		MqService mqService = getBean(MqServiceImpl.class);
		ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();

		// 启动3个消费者线程
		for (int i = 0; i < 100; i++) {
			final int threadIndex = i;
			executor.execute(() -> {
				log.info("DelayQueue Consumer Thread {} start", Thread.currentThread());
				for (; ; ) {
					try {
						DelayQueueMessage delayQueueMessage = DELAY_QUEUE.take();
						executor.execute(() -> mqService.doAction(delayQueueMessage));
					} catch (Exception e) {
						log.error("DelayQueue Consumer Thread {} error", threadIndex, e);
					}
				}
			});
		}
	}

	public static <T> T getBean(Class<T> t) {
		return APPLICATION_CONTEXT.getBean(t);
	}

	public static <T> T getBean(String beanName, Class<T> t) {
		return APPLICATION_CONTEXT.getBean(beanName, t);
	}
}
