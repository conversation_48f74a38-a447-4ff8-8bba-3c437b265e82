package com.weihengtech.ieee.utils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * Proto对象序列化工具类
 * 解决Proto对象在Redis缓存中的序列化问题
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/24
 */
@Slf4j
public class ProtoSerializationUtil {

    /**
     * Proto对象序列化器
     */
    public static class ProtoSerializer extends JsonSerializer<Message> {
        @Override
        public void serialize(Message value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            try {
                String jsonString = JsonFormat.printer().print(value);
                gen.writeRawValue(jsonString);
            } catch (InvalidProtocolBufferException e) {
                log.error("Proto对象序列化失败", e);
                gen.writeNull();
            }
        }
    }

    /**
     * Proto对象反序列化器基类
     * 具体的Proto类型需要继承此类并实现newBuilder方法
     */
    public static abstract class ProtoDeserializer<T extends Message> extends JsonDeserializer<T> {
        
        protected abstract Message.Builder newBuilder();
        
        @Override
        @SuppressWarnings("unchecked")
        public T deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            try {
                String jsonString = p.readValueAsTree().toString();
                Message.Builder builder = newBuilder();
                JsonFormat.parser().merge(jsonString, builder);
                return (T) builder.build();
            } catch (InvalidProtocolBufferException e) {
                log.error("Proto对象反序列化失败", e);
                return null;
            }
        }
    }
}
