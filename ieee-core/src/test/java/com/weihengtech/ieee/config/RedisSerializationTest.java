package com.weihengtech.ieee.config;

import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.pojo.dos.DeviceListDO;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import com.weihengtech.ieee.pojo.dtos.CacheItemDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis序列化测试
 * 测试CacheItemDTO对象（包含Proto对象）的序列化和反序列化
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/24
 */
@SpringBootTest
@ActiveProfiles("test")
public class RedisSerializationTest {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Test
    public void testCacheItemDTOSerialization() {
        // 创建测试数据
        StorageEnergyInfoDO storageInfo = new StorageEnergyInfoDO();
        storageInfo.setDeviceId(1L);
        storageInfo.setLfdi("test-lfdi");
        storageInfo.setDesignMaxPower(5000);

        DeviceListDO deviceInfo = new DeviceListDO();
        deviceInfo.setId(1L);
        deviceInfo.setDeviceSn("test-device-sn");
        deviceInfo.setWifiSn("test-wifi-sn");
        deviceInfo.setState(1);

        // 创建Proto对象
        EndDeviceProto.DERControlRequest request = EndDeviceProto.DERControlRequest.newBuilder()
                .setLfdi("test-lfdi")
                .setId(1L)
                .setMrid("test-mrid")
                .setType("start")
                .setStartTime(System.currentTimeMillis() / 1000)
                .setDuration(3600)
                .setControl(EndDeviceProto.DERControlBase.newBuilder()
                        .setOpModEnergize(true)
                        .setOpModConnect(true)
                        .setOpModMaxLimW(100)
                        .build())
                .build();

        // 创建CacheItemDTO
        CacheItemDTO cacheItem = CacheItemDTO.builder()
                .taskStartTime(System.currentTimeMillis())
                .storageInfo(storageInfo)
                .deviceInfo(deviceInfo)
                .request(request)
                .build();

        String testKey = "test:cache:item";

        try {
            // 测试序列化到Redis
            redisTemplate.opsForValue().set(testKey, cacheItem, 60, TimeUnit.SECONDS);
            
            // 测试从Redis反序列化
            Object retrieved = redisTemplate.opsForValue().get(testKey);
            
            assertNotNull(retrieved, "从Redis获取的对象不应为null");
            assertTrue(retrieved instanceof CacheItemDTO, "获取的对象应该是CacheItemDTO类型");
            
            CacheItemDTO retrievedItem = (CacheItemDTO) retrieved;
            assertEquals(cacheItem.getTaskStartTime(), retrievedItem.getTaskStartTime());
            assertNotNull(retrievedItem.getStorageInfo());
            assertNotNull(retrievedItem.getDeviceInfo());
            assertNotNull(retrievedItem.getRequest());
            
            System.out.println("Redis序列化测试成功！");
            
        } catch (Exception e) {
            fail("Redis序列化测试失败: " + e.getMessage());
        } finally {
            // 清理测试数据
            redisTemplate.delete(testKey);
        }
    }
}
