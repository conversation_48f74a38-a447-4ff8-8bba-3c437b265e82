package com.weihengtech.ieee;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@io.grpc.stub.annotations.GrpcGenerated
public final class EndDeviceServiceGrpc {

  private EndDeviceServiceGrpc() {}

  public static final java.lang.String SERVICE_NAME = "rpc.EndDeviceService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam,
      com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse> getGetEndDevicesMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetEndDevices",
      requestType = com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam.class,
      responseType = com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam,
      com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse> getGetEndDevicesMethod() {
    io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam, com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse> getGetEndDevicesMethod;
    if ((getGetEndDevicesMethod = EndDeviceServiceGrpc.getGetEndDevicesMethod) == null) {
      synchronized (EndDeviceServiceGrpc.class) {
        if ((getGetEndDevicesMethod = EndDeviceServiceGrpc.getGetEndDevicesMethod) == null) {
          EndDeviceServiceGrpc.getGetEndDevicesMethod = getGetEndDevicesMethod =
              io.grpc.MethodDescriptor.<com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam, com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetEndDevices"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse.getDefaultInstance()))
              .setSchemaDescriptor(new EndDeviceServiceMethodDescriptorSupplier("GetEndDevices"))
              .build();
        }
      }
    }
    return getGetEndDevicesMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam,
      com.weihengtech.ieee.EndDeviceProto.EndDevice> getGetEndDeviceMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetEndDevice",
      requestType = com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam.class,
      responseType = com.weihengtech.ieee.EndDeviceProto.EndDevice.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam,
      com.weihengtech.ieee.EndDeviceProto.EndDevice> getGetEndDeviceMethod() {
    io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam, com.weihengtech.ieee.EndDeviceProto.EndDevice> getGetEndDeviceMethod;
    if ((getGetEndDeviceMethod = EndDeviceServiceGrpc.getGetEndDeviceMethod) == null) {
      synchronized (EndDeviceServiceGrpc.class) {
        if ((getGetEndDeviceMethod = EndDeviceServiceGrpc.getGetEndDeviceMethod) == null) {
          EndDeviceServiceGrpc.getGetEndDeviceMethod = getGetEndDeviceMethod =
              io.grpc.MethodDescriptor.<com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam, com.weihengtech.ieee.EndDeviceProto.EndDevice>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetEndDevice"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EndDeviceProto.EndDevice.getDefaultInstance()))
              .setSchemaDescriptor(new EndDeviceServiceMethodDescriptorSupplier("GetEndDevice"))
              .build();
        }
      }
    }
    return getGetEndDeviceMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam,
      com.weihengtech.ieee.EndDeviceProto.EndDeviceData> getGetEndDeviceDataMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetEndDeviceData",
      requestType = com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam.class,
      responseType = com.weihengtech.ieee.EndDeviceProto.EndDeviceData.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam,
      com.weihengtech.ieee.EndDeviceProto.EndDeviceData> getGetEndDeviceDataMethod() {
    io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam, com.weihengtech.ieee.EndDeviceProto.EndDeviceData> getGetEndDeviceDataMethod;
    if ((getGetEndDeviceDataMethod = EndDeviceServiceGrpc.getGetEndDeviceDataMethod) == null) {
      synchronized (EndDeviceServiceGrpc.class) {
        if ((getGetEndDeviceDataMethod = EndDeviceServiceGrpc.getGetEndDeviceDataMethod) == null) {
          EndDeviceServiceGrpc.getGetEndDeviceDataMethod = getGetEndDeviceDataMethod =
              io.grpc.MethodDescriptor.<com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam, com.weihengtech.ieee.EndDeviceProto.EndDeviceData>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetEndDeviceData"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EndDeviceProto.EndDeviceData.getDefaultInstance()))
              .setSchemaDescriptor(new EndDeviceServiceMethodDescriptorSupplier("GetEndDeviceData"))
              .build();
        }
      }
    }
    return getGetEndDeviceDataMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.DefaultDERControl,
      com.weihengtech.ieee.EmptyProto.Empty> getSetDefaultControlMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SetDefaultControl",
      requestType = com.weihengtech.ieee.EndDeviceProto.DefaultDERControl.class,
      responseType = com.weihengtech.ieee.EmptyProto.Empty.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.DefaultDERControl,
      com.weihengtech.ieee.EmptyProto.Empty> getSetDefaultControlMethod() {
    io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.DefaultDERControl, com.weihengtech.ieee.EmptyProto.Empty> getSetDefaultControlMethod;
    if ((getSetDefaultControlMethod = EndDeviceServiceGrpc.getSetDefaultControlMethod) == null) {
      synchronized (EndDeviceServiceGrpc.class) {
        if ((getSetDefaultControlMethod = EndDeviceServiceGrpc.getSetDefaultControlMethod) == null) {
          EndDeviceServiceGrpc.getSetDefaultControlMethod = getSetDefaultControlMethod =
              io.grpc.MethodDescriptor.<com.weihengtech.ieee.EndDeviceProto.DefaultDERControl, com.weihengtech.ieee.EmptyProto.Empty>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SetDefaultControl"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EndDeviceProto.DefaultDERControl.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EmptyProto.Empty.getDefaultInstance()))
              .setSchemaDescriptor(new EndDeviceServiceMethodDescriptorSupplier("SetDefaultControl"))
              .build();
        }
      }
    }
    return getSetDefaultControlMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.DERControlRequest,
      com.weihengtech.ieee.EmptyProto.Empty> getSetControlMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SetControl",
      requestType = com.weihengtech.ieee.EndDeviceProto.DERControlRequest.class,
      responseType = com.weihengtech.ieee.EmptyProto.Empty.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.DERControlRequest,
      com.weihengtech.ieee.EmptyProto.Empty> getSetControlMethod() {
    io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.DERControlRequest, com.weihengtech.ieee.EmptyProto.Empty> getSetControlMethod;
    if ((getSetControlMethod = EndDeviceServiceGrpc.getSetControlMethod) == null) {
      synchronized (EndDeviceServiceGrpc.class) {
        if ((getSetControlMethod = EndDeviceServiceGrpc.getSetControlMethod) == null) {
          EndDeviceServiceGrpc.getSetControlMethod = getSetControlMethod =
              io.grpc.MethodDescriptor.<com.weihengtech.ieee.EndDeviceProto.DERControlRequest, com.weihengtech.ieee.EmptyProto.Empty>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SetControl"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EndDeviceProto.DERControlRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EmptyProto.Empty.getDefaultInstance()))
              .setSchemaDescriptor(new EndDeviceServiceMethodDescriptorSupplier("SetControl"))
              .build();
        }
      }
    }
    return getSetControlMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.StepStateRequest,
      com.weihengtech.ieee.EmptyProto.Empty> getUpdateStepStateMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UpdateStepState",
      requestType = com.weihengtech.ieee.EndDeviceProto.StepStateRequest.class,
      responseType = com.weihengtech.ieee.EmptyProto.Empty.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.StepStateRequest,
      com.weihengtech.ieee.EmptyProto.Empty> getUpdateStepStateMethod() {
    io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.StepStateRequest, com.weihengtech.ieee.EmptyProto.Empty> getUpdateStepStateMethod;
    if ((getUpdateStepStateMethod = EndDeviceServiceGrpc.getUpdateStepStateMethod) == null) {
      synchronized (EndDeviceServiceGrpc.class) {
        if ((getUpdateStepStateMethod = EndDeviceServiceGrpc.getUpdateStepStateMethod) == null) {
          EndDeviceServiceGrpc.getUpdateStepStateMethod = getUpdateStepStateMethod =
              io.grpc.MethodDescriptor.<com.weihengtech.ieee.EndDeviceProto.StepStateRequest, com.weihengtech.ieee.EmptyProto.Empty>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UpdateStepState"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EndDeviceProto.StepStateRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EmptyProto.Empty.getDefaultInstance()))
              .setSchemaDescriptor(new EndDeviceServiceMethodDescriptorSupplier("UpdateStepState"))
              .build();
        }
      }
    }
    return getUpdateStepStateMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.weihengtech.ieee.EmptyProto.Empty,
      com.weihengtech.ieee.EndDeviceProto.SubscribeEndDeviceResponse> getSubscribeEndDeviceMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SubscribeEndDevice",
      requestType = com.weihengtech.ieee.EmptyProto.Empty.class,
      responseType = com.weihengtech.ieee.EndDeviceProto.SubscribeEndDeviceResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
  public static io.grpc.MethodDescriptor<com.weihengtech.ieee.EmptyProto.Empty,
      com.weihengtech.ieee.EndDeviceProto.SubscribeEndDeviceResponse> getSubscribeEndDeviceMethod() {
    io.grpc.MethodDescriptor<com.weihengtech.ieee.EmptyProto.Empty, com.weihengtech.ieee.EndDeviceProto.SubscribeEndDeviceResponse> getSubscribeEndDeviceMethod;
    if ((getSubscribeEndDeviceMethod = EndDeviceServiceGrpc.getSubscribeEndDeviceMethod) == null) {
      synchronized (EndDeviceServiceGrpc.class) {
        if ((getSubscribeEndDeviceMethod = EndDeviceServiceGrpc.getSubscribeEndDeviceMethod) == null) {
          EndDeviceServiceGrpc.getSubscribeEndDeviceMethod = getSubscribeEndDeviceMethod =
              io.grpc.MethodDescriptor.<com.weihengtech.ieee.EmptyProto.Empty, com.weihengtech.ieee.EndDeviceProto.SubscribeEndDeviceResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SubscribeEndDevice"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EmptyProto.Empty.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EndDeviceProto.SubscribeEndDeviceResponse.getDefaultInstance()))
              .setSchemaDescriptor(new EndDeviceServiceMethodDescriptorSupplier("SubscribeEndDevice"))
              .build();
        }
      }
    }
    return getSubscribeEndDeviceMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus,
      com.weihengtech.ieee.EmptyProto.Empty> getUpdateUtilityServerStatusMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UpdateUtilityServerStatus",
      requestType = com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus.class,
      responseType = com.weihengtech.ieee.EmptyProto.Empty.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus,
      com.weihengtech.ieee.EmptyProto.Empty> getUpdateUtilityServerStatusMethod() {
    io.grpc.MethodDescriptor<com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus, com.weihengtech.ieee.EmptyProto.Empty> getUpdateUtilityServerStatusMethod;
    if ((getUpdateUtilityServerStatusMethod = EndDeviceServiceGrpc.getUpdateUtilityServerStatusMethod) == null) {
      synchronized (EndDeviceServiceGrpc.class) {
        if ((getUpdateUtilityServerStatusMethod = EndDeviceServiceGrpc.getUpdateUtilityServerStatusMethod) == null) {
          EndDeviceServiceGrpc.getUpdateUtilityServerStatusMethod = getUpdateUtilityServerStatusMethod =
              io.grpc.MethodDescriptor.<com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus, com.weihengtech.ieee.EmptyProto.Empty>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UpdateUtilityServerStatus"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.weihengtech.ieee.EmptyProto.Empty.getDefaultInstance()))
              .setSchemaDescriptor(new EndDeviceServiceMethodDescriptorSupplier("UpdateUtilityServerStatus"))
              .build();
        }
      }
    }
    return getUpdateUtilityServerStatusMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static EndDeviceServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<EndDeviceServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<EndDeviceServiceStub>() {
        @java.lang.Override
        public EndDeviceServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new EndDeviceServiceStub(channel, callOptions);
        }
      };
    return EndDeviceServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports all types of calls on the service
   */
  public static EndDeviceServiceBlockingV2Stub newBlockingV2Stub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<EndDeviceServiceBlockingV2Stub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<EndDeviceServiceBlockingV2Stub>() {
        @java.lang.Override
        public EndDeviceServiceBlockingV2Stub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new EndDeviceServiceBlockingV2Stub(channel, callOptions);
        }
      };
    return EndDeviceServiceBlockingV2Stub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static EndDeviceServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<EndDeviceServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<EndDeviceServiceBlockingStub>() {
        @java.lang.Override
        public EndDeviceServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new EndDeviceServiceBlockingStub(channel, callOptions);
        }
      };
    return EndDeviceServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static EndDeviceServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<EndDeviceServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<EndDeviceServiceFutureStub>() {
        @java.lang.Override
        public EndDeviceServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new EndDeviceServiceFutureStub(channel, callOptions);
        }
      };
    return EndDeviceServiceFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     */
    default void getEndDevices(com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetEndDevicesMethod(), responseObserver);
    }

    /**
     */
    default void getEndDevice(com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EndDeviceProto.EndDevice> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetEndDeviceMethod(), responseObserver);
    }

    /**
     */
    default void getEndDeviceData(com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EndDeviceProto.EndDeviceData> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetEndDeviceDataMethod(), responseObserver);
    }

    /**
     */
    default void setDefaultControl(com.weihengtech.ieee.EndDeviceProto.DefaultDERControl request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EmptyProto.Empty> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetDefaultControlMethod(), responseObserver);
    }

    /**
     */
    default void setControl(com.weihengtech.ieee.EndDeviceProto.DERControlRequest request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EmptyProto.Empty> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetControlMethod(), responseObserver);
    }

    /**
     */
    default void updateStepState(com.weihengtech.ieee.EndDeviceProto.StepStateRequest request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EmptyProto.Empty> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpdateStepStateMethod(), responseObserver);
    }

    /**
     */
    default void subscribeEndDevice(com.weihengtech.ieee.EmptyProto.Empty request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EndDeviceProto.SubscribeEndDeviceResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSubscribeEndDeviceMethod(), responseObserver);
    }

    /**
     */
    default void updateUtilityServerStatus(com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EmptyProto.Empty> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpdateUtilityServerStatusMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service EndDeviceService.
   */
  public static abstract class EndDeviceServiceImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return EndDeviceServiceGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service EndDeviceService.
   */
  public static final class EndDeviceServiceStub
      extends io.grpc.stub.AbstractAsyncStub<EndDeviceServiceStub> {
    private EndDeviceServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected EndDeviceServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new EndDeviceServiceStub(channel, callOptions);
    }

    /**
     */
    public void getEndDevices(com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetEndDevicesMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void getEndDevice(com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EndDeviceProto.EndDevice> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetEndDeviceMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void getEndDeviceData(com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EndDeviceProto.EndDeviceData> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetEndDeviceDataMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void setDefaultControl(com.weihengtech.ieee.EndDeviceProto.DefaultDERControl request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EmptyProto.Empty> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSetDefaultControlMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void setControl(com.weihengtech.ieee.EndDeviceProto.DERControlRequest request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EmptyProto.Empty> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSetControlMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void updateStepState(com.weihengtech.ieee.EndDeviceProto.StepStateRequest request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EmptyProto.Empty> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpdateStepStateMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void subscribeEndDevice(com.weihengtech.ieee.EmptyProto.Empty request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EndDeviceProto.SubscribeEndDeviceResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncServerStreamingCall(
          getChannel().newCall(getSubscribeEndDeviceMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void updateUtilityServerStatus(com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus request,
        io.grpc.stub.StreamObserver<com.weihengtech.ieee.EmptyProto.Empty> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpdateUtilityServerStatusMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service EndDeviceService.
   */
  public static final class EndDeviceServiceBlockingV2Stub
      extends io.grpc.stub.AbstractBlockingStub<EndDeviceServiceBlockingV2Stub> {
    private EndDeviceServiceBlockingV2Stub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected EndDeviceServiceBlockingV2Stub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new EndDeviceServiceBlockingV2Stub(channel, callOptions);
    }

    /**
     */
    public com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse getEndDevices(com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam request) throws io.grpc.StatusException {
      return io.grpc.stub.ClientCalls.blockingV2UnaryCall(
          getChannel(), getGetEndDevicesMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.weihengtech.ieee.EndDeviceProto.EndDevice getEndDevice(com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam request) throws io.grpc.StatusException {
      return io.grpc.stub.ClientCalls.blockingV2UnaryCall(
          getChannel(), getGetEndDeviceMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.weihengtech.ieee.EndDeviceProto.EndDeviceData getEndDeviceData(com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam request) throws io.grpc.StatusException {
      return io.grpc.stub.ClientCalls.blockingV2UnaryCall(
          getChannel(), getGetEndDeviceDataMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.weihengtech.ieee.EmptyProto.Empty setDefaultControl(com.weihengtech.ieee.EndDeviceProto.DefaultDERControl request) throws io.grpc.StatusException {
      return io.grpc.stub.ClientCalls.blockingV2UnaryCall(
          getChannel(), getSetDefaultControlMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.weihengtech.ieee.EmptyProto.Empty setControl(com.weihengtech.ieee.EndDeviceProto.DERControlRequest request) throws io.grpc.StatusException {
      return io.grpc.stub.ClientCalls.blockingV2UnaryCall(
          getChannel(), getSetControlMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.weihengtech.ieee.EmptyProto.Empty updateStepState(com.weihengtech.ieee.EndDeviceProto.StepStateRequest request) throws io.grpc.StatusException {
      return io.grpc.stub.ClientCalls.blockingV2UnaryCall(
          getChannel(), getUpdateStepStateMethod(), getCallOptions(), request);
    }

    /**
     */
    @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/10918")
    public io.grpc.stub.BlockingClientCall<?, com.weihengtech.ieee.EndDeviceProto.SubscribeEndDeviceResponse>
        subscribeEndDevice(com.weihengtech.ieee.EmptyProto.Empty request) {
      return io.grpc.stub.ClientCalls.blockingV2ServerStreamingCall(
          getChannel(), getSubscribeEndDeviceMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.weihengtech.ieee.EmptyProto.Empty updateUtilityServerStatus(com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus request) throws io.grpc.StatusException {
      return io.grpc.stub.ClientCalls.blockingV2UnaryCall(
          getChannel(), getUpdateUtilityServerStatusMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do limited synchronous rpc calls to service EndDeviceService.
   */
  public static final class EndDeviceServiceBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<EndDeviceServiceBlockingStub> {
    private EndDeviceServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected EndDeviceServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new EndDeviceServiceBlockingStub(channel, callOptions);
    }

    /**
     */
    public com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse getEndDevices(com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetEndDevicesMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.weihengtech.ieee.EndDeviceProto.EndDevice getEndDevice(com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetEndDeviceMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.weihengtech.ieee.EndDeviceProto.EndDeviceData getEndDeviceData(com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetEndDeviceDataMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.weihengtech.ieee.EmptyProto.Empty setDefaultControl(com.weihengtech.ieee.EndDeviceProto.DefaultDERControl request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSetDefaultControlMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.weihengtech.ieee.EmptyProto.Empty setControl(com.weihengtech.ieee.EndDeviceProto.DERControlRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSetControlMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.weihengtech.ieee.EmptyProto.Empty updateStepState(com.weihengtech.ieee.EndDeviceProto.StepStateRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpdateStepStateMethod(), getCallOptions(), request);
    }

    /**
     */
    public java.util.Iterator<com.weihengtech.ieee.EndDeviceProto.SubscribeEndDeviceResponse> subscribeEndDevice(
        com.weihengtech.ieee.EmptyProto.Empty request) {
      return io.grpc.stub.ClientCalls.blockingServerStreamingCall(
          getChannel(), getSubscribeEndDeviceMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.weihengtech.ieee.EmptyProto.Empty updateUtilityServerStatus(com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpdateUtilityServerStatusMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service EndDeviceService.
   */
  public static final class EndDeviceServiceFutureStub
      extends io.grpc.stub.AbstractFutureStub<EndDeviceServiceFutureStub> {
    private EndDeviceServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected EndDeviceServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new EndDeviceServiceFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse> getEndDevices(
        com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetEndDevicesMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.weihengtech.ieee.EndDeviceProto.EndDevice> getEndDevice(
        com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetEndDeviceMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.weihengtech.ieee.EndDeviceProto.EndDeviceData> getEndDeviceData(
        com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetEndDeviceDataMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.weihengtech.ieee.EmptyProto.Empty> setDefaultControl(
        com.weihengtech.ieee.EndDeviceProto.DefaultDERControl request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSetDefaultControlMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.weihengtech.ieee.EmptyProto.Empty> setControl(
        com.weihengtech.ieee.EndDeviceProto.DERControlRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSetControlMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.weihengtech.ieee.EmptyProto.Empty> updateStepState(
        com.weihengtech.ieee.EndDeviceProto.StepStateRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpdateStepStateMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.weihengtech.ieee.EmptyProto.Empty> updateUtilityServerStatus(
        com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpdateUtilityServerStatusMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_GET_END_DEVICES = 0;
  private static final int METHODID_GET_END_DEVICE = 1;
  private static final int METHODID_GET_END_DEVICE_DATA = 2;
  private static final int METHODID_SET_DEFAULT_CONTROL = 3;
  private static final int METHODID_SET_CONTROL = 4;
  private static final int METHODID_UPDATE_STEP_STATE = 5;
  private static final int METHODID_SUBSCRIBE_END_DEVICE = 6;
  private static final int METHODID_UPDATE_UTILITY_SERVER_STATUS = 7;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_GET_END_DEVICES:
          serviceImpl.getEndDevices((com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam) request,
              (io.grpc.stub.StreamObserver<com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse>) responseObserver);
          break;
        case METHODID_GET_END_DEVICE:
          serviceImpl.getEndDevice((com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam) request,
              (io.grpc.stub.StreamObserver<com.weihengtech.ieee.EndDeviceProto.EndDevice>) responseObserver);
          break;
        case METHODID_GET_END_DEVICE_DATA:
          serviceImpl.getEndDeviceData((com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam) request,
              (io.grpc.stub.StreamObserver<com.weihengtech.ieee.EndDeviceProto.EndDeviceData>) responseObserver);
          break;
        case METHODID_SET_DEFAULT_CONTROL:
          serviceImpl.setDefaultControl((com.weihengtech.ieee.EndDeviceProto.DefaultDERControl) request,
              (io.grpc.stub.StreamObserver<com.weihengtech.ieee.EmptyProto.Empty>) responseObserver);
          break;
        case METHODID_SET_CONTROL:
          serviceImpl.setControl((com.weihengtech.ieee.EndDeviceProto.DERControlRequest) request,
              (io.grpc.stub.StreamObserver<com.weihengtech.ieee.EmptyProto.Empty>) responseObserver);
          break;
        case METHODID_UPDATE_STEP_STATE:
          serviceImpl.updateStepState((com.weihengtech.ieee.EndDeviceProto.StepStateRequest) request,
              (io.grpc.stub.StreamObserver<com.weihengtech.ieee.EmptyProto.Empty>) responseObserver);
          break;
        case METHODID_SUBSCRIBE_END_DEVICE:
          serviceImpl.subscribeEndDevice((com.weihengtech.ieee.EmptyProto.Empty) request,
              (io.grpc.stub.StreamObserver<com.weihengtech.ieee.EndDeviceProto.SubscribeEndDeviceResponse>) responseObserver);
          break;
        case METHODID_UPDATE_UTILITY_SERVER_STATUS:
          serviceImpl.updateUtilityServerStatus((com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus) request,
              (io.grpc.stub.StreamObserver<com.weihengtech.ieee.EmptyProto.Empty>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getGetEndDevicesMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.weihengtech.ieee.EndDeviceProto.GetEndDevicesParam,
              com.weihengtech.ieee.EndDeviceProto.GetEndDeviceResponse>(
                service, METHODID_GET_END_DEVICES)))
        .addMethod(
          getGetEndDeviceMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam,
              com.weihengtech.ieee.EndDeviceProto.EndDevice>(
                service, METHODID_GET_END_DEVICE)))
        .addMethod(
          getGetEndDeviceDataMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.weihengtech.ieee.EndDeviceProto.GetEndDeviceParam,
              com.weihengtech.ieee.EndDeviceProto.EndDeviceData>(
                service, METHODID_GET_END_DEVICE_DATA)))
        .addMethod(
          getSetDefaultControlMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.weihengtech.ieee.EndDeviceProto.DefaultDERControl,
              com.weihengtech.ieee.EmptyProto.Empty>(
                service, METHODID_SET_DEFAULT_CONTROL)))
        .addMethod(
          getSetControlMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.weihengtech.ieee.EndDeviceProto.DERControlRequest,
              com.weihengtech.ieee.EmptyProto.Empty>(
                service, METHODID_SET_CONTROL)))
        .addMethod(
          getUpdateStepStateMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.weihengtech.ieee.EndDeviceProto.StepStateRequest,
              com.weihengtech.ieee.EmptyProto.Empty>(
                service, METHODID_UPDATE_STEP_STATE)))
        .addMethod(
          getSubscribeEndDeviceMethod(),
          io.grpc.stub.ServerCalls.asyncServerStreamingCall(
            new MethodHandlers<
              com.weihengtech.ieee.EmptyProto.Empty,
              com.weihengtech.ieee.EndDeviceProto.SubscribeEndDeviceResponse>(
                service, METHODID_SUBSCRIBE_END_DEVICE)))
        .addMethod(
          getUpdateUtilityServerStatusMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.weihengtech.ieee.EndDeviceProto.UtilityServerStatus,
              com.weihengtech.ieee.EmptyProto.Empty>(
                service, METHODID_UPDATE_UTILITY_SERVER_STATUS)))
        .build();
  }

  private static abstract class EndDeviceServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    EndDeviceServiceBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.weihengtech.ieee.EndDeviceProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("EndDeviceService");
    }
  }

  private static final class EndDeviceServiceFileDescriptorSupplier
      extends EndDeviceServiceBaseDescriptorSupplier {
    EndDeviceServiceFileDescriptorSupplier() {}
  }

  private static final class EndDeviceServiceMethodDescriptorSupplier
      extends EndDeviceServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    EndDeviceServiceMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (EndDeviceServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new EndDeviceServiceFileDescriptorSupplier())
              .addMethod(getGetEndDevicesMethod())
              .addMethod(getGetEndDeviceMethod())
              .addMethod(getGetEndDeviceDataMethod())
              .addMethod(getSetDefaultControlMethod())
              .addMethod(getSetControlMethod())
              .addMethod(getUpdateStepStateMethod())
              .addMethod(getSubscribeEndDeviceMethod())
              .addMethod(getUpdateUtilityServerStatusMethod())
              .build();
        }
      }
    }
    return result;
  }
}
